# Development Guide

This guide covers the development workflow, conventions, and best practices for the Nexed Web project.

## 🚀 Getting Started

### Environment Setup

1. **Node.js**: Ensure you have Node.js v18 or higher installed
2. **Package Manager**: We use npm as the primary package manager
3. **IDE**: VS Code is recommended with the following extensions:
   - TypeScript and JavaScript Language Features
   - ESLint
   - Prettier
   - Tailwind CSS IntelliSense
   - Auto Rename Tag

### Development Workflow

```bash
# 1. Start development server
npm run dev

# 2. Run tests in watch mode (in another terminal)
npm run test

# 3. Start Storybook for component development
npm run storybook
```

## 📁 Project Architecture

### Component Organization

Components are organized in a hierarchical structure:

```
src/components/
├── global/              # Globally reusable components
│   ├── Button/
│   ├── Input/
│   └── Card/
├── layout/              # Layout-specific components
│   ├── Header/
│   ├── Sidebar/
│   └── Footer/
└── [FeatureName]/       # Feature-specific components
    ├── ComponentA/
    └── ComponentB/
```

### Naming Conventions

- **Components**: PascalCase (e.g., `UserProfile`, `NavigationMenu`)
- **Files**: PascalCase for components, camelCase for utilities
- **Folders**: PascalCase for component folders
- **Variables**: camelCase
- **Constants**: UPPER_SNAKE_CASE
- **Types/Interfaces**: PascalCase with descriptive names

### Component Structure

Each component should follow this structure:

```
ComponentName/
├── index.ts             # Export file
├── ComponentName.tsx    # Main component
├── ComponentName.test.tsx # Tests
├── ComponentName.stories.tsx # Storybook stories
└── types.ts            # Component-specific types
```

## 🎨 Styling Guidelines

### Tailwind CSS

- Use Tailwind utility classes for styling
- Create custom components for repeated patterns
- Use the `cn()` utility for conditional classes
- Follow mobile-first responsive design

### Theme System

The project uses a comprehensive theme system:

```typescript
// Access theme colors
const { colors } = useThemeStore();

// Use in components
<div className="bg-surface text-text border-border">
  Content
</div>
```

## 🧪 Testing Strategy

### Unit Tests

- Write tests for all components and utilities
- Use React Testing Library for component tests
- Mock external dependencies
- Aim for high test coverage

```typescript
// Example component test
import { render, screen } from '@testing-library/react';
import { Button } from './Button';

test('renders button with text', () => {
  render(<Button>Click me</Button>);
  expect(screen.getByRole('button')).toHaveTextContent('Click me');
});
```

### E2E Tests

- Use Playwright for end-to-end testing
- Test critical user journeys
- Test across different browsers

### Storybook

- Create stories for all components
- Document component props and usage
- Include different states and variants

## 🔧 Code Quality

### ESLint Rules

The project follows strict ESLint rules:

- TypeScript strict mode
- React hooks rules
- Prettier integration
- Custom rules for consistency

### Pre-commit Hooks

Husky and lint-staged ensure code quality:

```json
{
  "*.{ts,tsx,js,jsx}": [
    "eslint --fix",
    "prettier --write"
  ],
  "*.{json,css,md}": [
    "prettier --write"
  ]
}
```

## 🚀 Build and Deployment

### Development Build

```bash
npm run dev
```

### Production Build

```bash
npm run build
npm run preview  # Preview production build
```

### Environment Variables

Create `.env.local` for local development:

```env
VITE_API_URL=http://localhost:3001
VITE_APP_ENV=development
```

## 🛠️ Common Tasks

### Adding a New Component

1. Create component folder in appropriate location
2. Implement component with TypeScript
3. Add tests
4. Create Storybook story
5. Export from index file
6. Update documentation

### Adding a New Page

1. Create page component in `src/pages/`
2. Add route in `src/router/`
3. Add navigation if needed
4. Add E2E tests

### Adding Dependencies

```bash
# Production dependency
npm install package-name

# Development dependency
npm install -D package-name
```

### Debugging

- Use React DevTools for component debugging
- Use browser DevTools for general debugging
- Check console for errors and warnings
- Use Storybook for isolated component testing

## 📝 Best Practices

### React

- Use functional components with hooks
- Implement proper error boundaries
- Use React.memo for performance optimization
- Follow React patterns and conventions

### TypeScript

- Use strict TypeScript configuration
- Define proper types for all props and state
- Avoid `any` type
- Use type guards when necessary

### Performance

- Lazy load components when appropriate
- Optimize bundle size
- Use React.memo and useMemo wisely
- Monitor performance with React DevTools

### Accessibility

- Use semantic HTML elements
- Provide proper ARIA labels
- Ensure keyboard navigation
- Test with screen readers
- Maintain proper color contrast

## 🔍 Troubleshooting

### Common Issues

1. **Build Errors**: Check TypeScript errors and fix them
2. **Test Failures**: Ensure all dependencies are mocked properly
3. **Styling Issues**: Check Tailwind class names and theme configuration
4. **Performance Issues**: Use React DevTools Profiler

### Getting Help

- Check existing documentation
- Review similar implementations in the codebase
- Ask team members for guidance
- Create detailed issue reports
