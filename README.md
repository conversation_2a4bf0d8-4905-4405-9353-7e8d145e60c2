# Nexed Web

A modern React application built with TypeScript, Vite, and Tailwind CSS. This project features a comprehensive component library, robust error handling, theme management, and testing infrastructure.

## 🚀 Quick Start

### Prerequisites

- Node.js (v18 or higher)
- npm, yarn, or pnpm

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd nexed-web

# Install dependencies
npm install

# Start development server
npm run dev
```

### Development

```bash
# Start development server
npm run dev

# Run tests
npm run test

# Run tests with UI
npm run test:ui

# Run E2E tests
npm run test:e2e

# Start Storybook
npm run storybook
```

### Build

```bash
# Build for production
npm run build

# Preview production build
npm run preview
```

## 🏗️ Project Structure

```
nexed-web/
├── docs/                    # Documentation
├── public/                  # Static assets
├── src/
│   ├── app/                # App configuration
│   ├── assets/             # Images, icons, etc.
│   ├── components/         # Reusable components
│   ├── config/             # Configuration files
│   ├── data/               # Static data
│   ├── hooks/              # Custom React hooks
│   ├── mocks/              # MSW mock data
│   ├── modules/            # Feature modules
│   ├── pages/              # Page components
│   ├── providers/          # Context providers
│   ├── router/             # Routing configuration
│   ├── services/           # API services
│   ├── stores/             # State management
│   ├── test/               # Test utilities
│   ├── types/              # TypeScript types
│   └── utils/              # Utility functions
├── tests/                  # E2E tests
└── ...config files
```

## 🛠️ Tech Stack

- **Framework**: React 19 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS v4
- **Routing**: React Router v7
- **State Management**: Zustand + SWR
- **Testing**: Vitest + Playwright + Testing Library
- **Storybook**: Component development
- **Mocking**: MSW (Mock Service Worker)
- **Linting**: ESLint + Prettier
- **Git Hooks**: Husky + lint-staged

## 📚 Documentation

- [Routing and SWR](./docs/ROUTING_AND_SWR.md)
- [Theme System](./docs/THEME_SYSTEM.md)
- [Development Guide](./docs/DEVELOPMENT.md)
- [Component Library](./docs/COMPONENTS.md)
- [Testing Guide](./docs/TESTING.md)

## 🧪 Testing

### Unit Tests
```bash
npm run test              # Run tests in watch mode
npm run test:run          # Run tests once
npm run test:coverage     # Run with coverage
```

### E2E Tests
```bash
npm run test:e2e          # Run E2E tests
npm run test:e2e:ui       # Run with Playwright UI
npm run test:e2e:headed   # Run in headed mode
```

### Storybook
```bash
npm run storybook         # Start Storybook dev server
npm run build-storybook   # Build Storybook
```

## 🎨 Development Tools

### Code Quality
- ESLint for code linting
- Prettier for code formatting
- TypeScript for type checking
- Husky for git hooks
- lint-staged for pre-commit checks

### Scripts
```bash
npm run lint              # Lint code
npm run lint:fix          # Fix linting issues
npm run format            # Format code
npm run format:check      # Check formatting
npm run type-check        # Type check
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
