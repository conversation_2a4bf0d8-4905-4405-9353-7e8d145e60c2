import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { Card } from '../Card';

// Mock the theme store
vi.mock('../../../../stores/themeStore', () => ({
  useThemeStore: () => ({
    colors: {
      surface: '#ffffff',
      border: '#e5e7eb',
      shadow: '#000000',
      muted: '#f3f4f6',
    },
  }),
}));

describe('Card', () => {
  it('renders children correctly', () => {
    render(
      <Card data-testid="test-card">
        <div>Test content</div>
      </Card>
    );

    expect(screen.getByTestId('test-card')).toBeInTheDocument();
    expect(screen.getByText('Test content')).toBeInTheDocument();
  });

  it('applies correct variant styles', () => {
    const { rerender } = render(
      <Card variant="default" data-testid="test-card">
        Content
      </Card>
    );

    let card = screen.getByTestId('test-card');
    expect(card).toHaveClass('rounded-lg');

    rerender(
      <Card variant="elevated" data-testid="test-card">
        Content
      </Card>
    );

    card = screen.getByTestId('test-card');
    expect(card).toHaveClass('rounded-lg');
  });

  it('applies correct padding classes', () => {
    const { rerender } = render(
      <Card padding="sm" data-testid="test-card">
        Content
      </Card>
    );

    let card = screen.getByTestId('test-card');
    expect(card).toHaveClass('p-3');

    rerender(
      <Card padding="md" data-testid="test-card">
        Content
      </Card>
    );

    card = screen.getByTestId('test-card');
    expect(card).toHaveClass('p-4');

    rerender(
      <Card padding="lg" data-testid="test-card">
        Content
      </Card>
    );

    card = screen.getByTestId('test-card');
    expect(card).toHaveClass('p-6');

    rerender(
      <Card padding="none" data-testid="test-card">
        Content
      </Card>
    );

    card = screen.getByTestId('test-card');
    expect(card).not.toHaveClass('p-3');
    expect(card).not.toHaveClass('p-4');
    expect(card).not.toHaveClass('p-6');
  });

  it('applies hoverable styles when hoverable is true', () => {
    render(
      <Card hoverable data-testid="test-card">
        Content
      </Card>
    );

    const card = screen.getByTestId('test-card');
    expect(card).toHaveClass('hover:shadow-lg');
    expect(card).toHaveClass('hover:transform');
    expect(card).toHaveClass('hover:-translate-y-1');
  });

  it('applies clickable styles and handles click events', () => {
    const handleClick = vi.fn();

    render(
      <Card clickable onClick={handleClick} data-testid="test-card">
        Content
      </Card>
    );

    const card = screen.getByTestId('test-card');
    expect(card).toHaveClass('cursor-pointer');
    expect(card).toHaveClass('hover:shadow-lg');

    fireEvent.click(card);
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('does not trigger click when not clickable', () => {
    const handleClick = vi.fn();

    render(
      <Card onClick={handleClick} data-testid="test-card">
        Content
      </Card>
    );

    const card = screen.getByTestId('test-card');
    expect(card).not.toHaveClass('cursor-pointer');

    fireEvent.click(card);
    expect(handleClick).not.toHaveBeenCalled();
  });

  it('applies custom className', () => {
    render(
      <Card className="custom-class" data-testid="test-card">
        Content
      </Card>
    );

    const card = screen.getByTestId('test-card');
    expect(card).toHaveClass('custom-class');
    expect(card).toHaveClass('rounded-lg'); // Should still have base classes
  });

  it('forwards additional props', () => {
    render(
      <Card data-testid="test-card" role="article" aria-label="Test card">
        Content
      </Card>
    );

    const card = screen.getByTestId('test-card');
    expect(card).toHaveAttribute('role', 'article');
    expect(card).toHaveAttribute('aria-label', 'Test card');
  });

  it('handles click event with correct event object', () => {
    const handleClick = jest.fn();

    render(
      <Card clickable onClick={handleClick} data-testid="test-card">
        Content
      </Card>
    );

    const card = screen.getByTestId('test-card');
    fireEvent.click(card);

    expect(handleClick).toHaveBeenCalledWith(expect.any(Object));
    expect(handleClick.mock.calls[0][0]).toHaveProperty('type', 'click');
  });
});
