# Component Library

This document provides an overview of the reusable component library used throughout the Nexed Web application.

## 🏗️ Architecture

The component library is organized into several categories:

- **Global Components**: Reusable across the entire application
- **Layout Components**: For page structure and navigation
- **Form Components**: Input fields, buttons, and form controls
- **Data Display**: Tables, cards, lists, and data visualization
- **Feedback**: Modals, alerts, loading states, and notifications
- **Utility Components**: Helpers and specialized functionality

## 🎨 Design System

### Theme Integration

All components integrate with the theme system:

```typescript
import { useThemeStore } from '@/stores/themeStore';

const MyComponent = () => {
  const { colors, theme } = useThemeStore();
  
  return (
    <div style={{ backgroundColor: colors.surface }}>
      Content
    </div>
  );
};
```

### Styling Approach

- **Tailwind CSS**: Primary styling method
- **CSS Variables**: For theme-aware properties
- **Conditional Classes**: Using `cn()` utility
- **Responsive Design**: Mobile-first approach

## 📦 Global Components

### Button

Versatile button component with multiple variants:

```typescript
import { Button } from '@/components/global/Button';

// Usage examples
<Button variant="primary">Primary Action</Button>
<Button variant="secondary" size="sm">Secondary</Button>
<Button variant="outline" disabled>Disabled</Button>
```

**Props:**
- `variant`: 'primary' | 'secondary' | 'outline' | 'ghost'
- `size`: 'sm' | 'md' | 'lg'
- `disabled`: boolean
- `loading`: boolean

### Input

Form input component with validation support:

```typescript
import { Input } from '@/components/global/Input';

<Input
  label="Email"
  type="email"
  placeholder="Enter your email"
  error="Invalid email format"
  required
/>
```

### Card

Container component for content grouping:

```typescript
import { Card } from '@/components/global/Card';

<Card>
  <Card.Header>
    <Card.Title>Card Title</Card.Title>
  </Card.Header>
  <Card.Content>
    Card content goes here
  </Card.Content>
</Card>
```

### Modal

Accessible modal component:

```typescript
import { Modal } from '@/components/global/Modal';

<Modal isOpen={isOpen} onClose={handleClose}>
  <Modal.Header>
    <Modal.Title>Modal Title</Modal.Title>
  </Modal.Header>
  <Modal.Body>
    Modal content
  </Modal.Body>
  <Modal.Footer>
    <Button onClick={handleClose}>Close</Button>
  </Modal.Footer>
</Modal>
```

## 🏠 Layout Components

### Header

Application header with navigation:

```typescript
import { Header } from '@/components/layout/Header';

<Header>
  <Header.Logo />
  <Header.Navigation />
  <Header.Actions />
</Header>
```

### Sidebar

Collapsible sidebar navigation:

```typescript
import { Sidebar } from '@/components/layout/Sidebar';

<Sidebar isCollapsed={isCollapsed}>
  <Sidebar.Header />
  <Sidebar.Navigation items={navigationItems} />
  <Sidebar.Footer />
</Sidebar>
```

### Breadcrumbs

Navigation breadcrumbs:

```typescript
import { Breadcrumbs } from '@/components/layout/Breadcrumbs';

<Breadcrumbs>
  <Breadcrumbs.Item href="/">Home</Breadcrumbs.Item>
  <Breadcrumbs.Item href="/products">Products</Breadcrumbs.Item>
  <Breadcrumbs.Item>Current Page</Breadcrumbs.Item>
</Breadcrumbs>
```

## 📊 Data Display Components

### DataTable

Feature-rich data table with sorting, filtering, and pagination:

```typescript
import { DataTable } from '@/components/global/DataTable';

<DataTable
  data={data}
  columns={columns}
  sortable
  filterable
  pagination
  onRowClick={handleRowClick}
/>
```

### List

Flexible list component:

```typescript
import { List } from '@/components/global/List';

<List>
  <List.Item>
    <List.ItemIcon icon={UserIcon} />
    <List.ItemContent>
      <List.ItemTitle>John Doe</List.ItemTitle>
      <List.ItemDescription>Software Engineer</List.ItemDescription>
    </List.ItemContent>
  </List.Item>
</List>
```

### Badge

Status and category indicators:

```typescript
import { Badge } from '@/components/global/Badge';

<Badge variant="success">Active</Badge>
<Badge variant="warning">Pending</Badge>
<Badge variant="error">Inactive</Badge>
```

## 📝 Form Components

### Form

Form wrapper with validation:

```typescript
import { Form } from '@/components/global/Form';

<Form onSubmit={handleSubmit} validation={validationSchema}>
  <Form.Field name="email">
    <Form.Label>Email</Form.Label>
    <Form.Input type="email" />
    <Form.Error />
  </Form.Field>
  
  <Form.Actions>
    <Button type="submit">Submit</Button>
  </Form.Actions>
</Form>
```

### Select

Dropdown selection component:

```typescript
import { Select } from '@/components/global/Select';

<Select
  options={options}
  value={selectedValue}
  onChange={handleChange}
  placeholder="Select an option"
  searchable
  multiple
/>
```

### DatePicker

Date selection component:

```typescript
import { DatePicker } from '@/components/global/DatePicker';

<DatePicker
  value={selectedDate}
  onChange={handleDateChange}
  placeholder="Select date"
  format="MM/dd/yyyy"
/>
```

## 🔔 Feedback Components

### Alert

Contextual alerts and notifications:

```typescript
import { Alert } from '@/components/global/Alert';

<Alert variant="success">
  <Alert.Icon />
  <Alert.Title>Success!</Alert.Title>
  <Alert.Description>Operation completed successfully.</Alert.Description>
</Alert>
```

### Loading

Loading states and spinners:

```typescript
import { Loading } from '@/components/global/Loading';

<Loading size="lg" text="Loading..." />
<Loading.Spinner />
<Loading.Skeleton />
```

### Toast

Toast notifications:

```typescript
import { useToast } from '@/hooks/useToast';

const { toast } = useToast();

toast.success('Operation successful!');
toast.error('Something went wrong');
toast.info('Information message');
```

## 🛠️ Utility Components

### ErrorBoundary

Error boundary for error handling:

```typescript
import { ErrorBoundary } from '@/components/global/ErrorBoundary';

<ErrorBoundary fallback={<ErrorFallback />}>
  <App />
</ErrorBoundary>
```

### ThemeToggle

Theme switching component:

```typescript
import { ThemeToggle } from '@/components/global/ThemeToggle';

<ThemeToggle />
```

### Pagination

Pagination controls:

```typescript
import { Pagination } from '@/components/global/Pagination';

<Pagination
  currentPage={currentPage}
  totalPages={totalPages}
  onPageChange={handlePageChange}
/>
```

## 🎯 Best Practices

### Component Development

1. **Single Responsibility**: Each component should have one clear purpose
2. **Composition**: Use composition over inheritance
3. **Props Interface**: Define clear TypeScript interfaces
4. **Default Props**: Provide sensible defaults
5. **Error Handling**: Handle edge cases gracefully

### Accessibility

1. **Semantic HTML**: Use appropriate HTML elements
2. **ARIA Labels**: Provide descriptive labels
3. **Keyboard Navigation**: Support keyboard interactions
4. **Focus Management**: Handle focus properly
5. **Screen Reader Support**: Test with screen readers

### Performance

1. **React.memo**: Memoize components when appropriate
2. **Lazy Loading**: Load components on demand
3. **Bundle Size**: Keep components lightweight
4. **Re-renders**: Minimize unnecessary re-renders

### Testing

1. **Unit Tests**: Test component behavior
2. **Integration Tests**: Test component interactions
3. **Accessibility Tests**: Use vitest-axe
4. **Visual Tests**: Use Storybook for visual testing

## 📚 Resources

- [Storybook Documentation](http://localhost:6006)
- [Theme System Guide](./THEME_SYSTEM.md)
- [Development Guide](./DEVELOPMENT.md)
- [Testing Guide](./TESTING.md)
