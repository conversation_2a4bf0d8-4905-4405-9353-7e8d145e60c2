import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { Button } from '../Button';

// Mock the theme store
vi.mock('../../../../stores/themeStore', () => ({
  useThemeStore: () => ({
    colors: {
      primary: '#3b82f6',
      primaryForeground: '#ffffff',
      secondary: '#6b7280',
      secondaryForeground: '#ffffff',
      error: '#ef4444',
      errorForeground: '#ffffff',
      text: '#1f2937',
    },
  }),
}));

// Mock the LoadingIcon
vi.mock('../../../icons', () => ({
  LoadingIcon: ({ className }: { className: string }) => (
    <div className={className} data-testid="loading-icon">Loading</div>
  ),
}));

describe('Button', () => {
  it('renders children correctly', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });

  it('applies correct variant styles', () => {
    const { rerender } = render(<Button variant="primary">Primary</Button>);
    let button = screen.getByRole('button');
    expect(button).toHaveClass('border');

    rerender(<Button variant="outline">Outline</Button>);
    button = screen.getByRole('button');
    expect(button).toHaveClass('border');

    rerender(<Button variant="ghost">Ghost</Button>);
    button = screen.getByRole('button');
    expect(button).toHaveClass('border-0');
  });

  it('applies correct size classes', () => {
    const { rerender } = render(<Button size="sm">Small</Button>);
    let button = screen.getByRole('button');
    expect(button).toHaveClass('h-8');

    rerender(<Button size="md">Medium</Button>);
    button = screen.getByRole('button');
    expect(button).toHaveClass('h-10');

    rerender(<Button size="lg">Large</Button>);
    button = screen.getByRole('button');
    expect(button).toHaveClass('h-12');
  });

  it('handles click events', () => {
    const handleClick = vi.fn();
    render(<Button onClick={handleClick}>Click me</Button>);

    const button = screen.getByRole('button');
    fireEvent.click(button);

    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('does not call onClick when disabled', () => {
    const handleClick = vi.fn();
    render(
      <Button disabled onClick={handleClick}>
        Disabled
      </Button>
    );

    const button = screen.getByRole('button');
    fireEvent.click(button);

    expect(handleClick).not.toHaveBeenCalled();
    expect(button).toBeDisabled();
  });

  it('does not call onClick when loading', () => {
    const handleClick = vi.fn();
    render(
      <Button loading onClick={handleClick}>
        Loading
      </Button>
    );

    const button = screen.getByRole('button');
    fireEvent.click(button);

    expect(handleClick).not.toHaveBeenCalled();
    expect(button).toBeDisabled();
  });

  it('shows loading icon when loading', () => {
    render(<Button loading>Loading</Button>);

    expect(screen.getByTestId('loading-icon')).toBeInTheDocument();
    expect(screen.getByText('Loading')).toBeInTheDocument();
  });

  it('applies fullWidth class when fullWidth is true', () => {
    render(<Button fullWidth>Full Width</Button>);

    const button = screen.getByRole('button');
    expect(button).toHaveClass('w-full');
  });

  it('renders with start icon', () => {
    const startIcon = <span data-testid="start-icon">→</span>;
    render(<Button startIcon={startIcon}>With Icon</Button>);

    expect(screen.getByTestId('start-icon')).toBeInTheDocument();
    expect(screen.getByText('With Icon')).toBeInTheDocument();
  });

  it('renders with end icon', () => {
    const endIcon = <span data-testid="end-icon">←</span>;
    render(<Button endIcon={endIcon}>With Icon</Button>);

    expect(screen.getByTestId('end-icon')).toBeInTheDocument();
    expect(screen.getByText('With Icon')).toBeInTheDocument();
  });

  it('hides end icon when loading', () => {
    const endIcon = <span data-testid="end-icon">←</span>;
    render(
      <Button loading endIcon={endIcon}>
        Loading
      </Button>
    );

    expect(screen.queryByTestId('end-icon')).not.toBeInTheDocument();
    expect(screen.getByTestId('loading-icon')).toBeInTheDocument();
  });

  it('sets correct button type', () => {
    const { rerender } = render(<Button type="submit">Submit</Button>);
    let button = screen.getByRole('button');
    expect(button).toHaveAttribute('type', 'submit');

    rerender(<Button type="reset">Reset</Button>);
    button = screen.getByRole('button');
    expect(button).toHaveAttribute('type', 'reset');

    rerender(<Button>Default</Button>);
    button = screen.getByRole('button');
    expect(button).toHaveAttribute('type', 'button');
  });

  it('applies custom className', () => {
    render(<Button className="custom-class">Custom</Button>);

    const button = screen.getByRole('button');
    expect(button).toHaveClass('custom-class');
  });

  it('forwards data-testid', () => {
    render(<Button data-testid="test-button">Test</Button>);

    expect(screen.getByTestId('test-button')).toBeInTheDocument();
  });

  it('applies loading cursor when loading', () => {
    render(<Button loading>Loading</Button>);

    const button = screen.getByRole('button');
    expect(button).toHaveClass('cursor-wait');
  });

  it('handles different rounded variants', () => {
    const { rerender } = render(<Button rounded="none">None</Button>);
    let button = screen.getByRole('button');
    expect(button).toHaveClass('rounded-none');

    rerender(<Button rounded="full">Full</Button>);
    button = screen.getByRole('button');
    expect(button).toHaveClass('rounded-full');
  });

  it('handles different shadow variants', () => {
    const { rerender } = render(<Button shadow="none">None</Button>);
    let button = screen.getByRole('button');
    expect(button).toHaveClass('shadow-none');

    rerender(<Button shadow="lg">Large</Button>);
    button = screen.getByRole('button');
    expect(button).toHaveClass('shadow-lg');
  });
});
