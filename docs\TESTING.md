# Testing Guide

This guide covers the testing strategy, tools, and best practices for the Nexed Web project.

## 🧪 Testing Strategy

The project uses a comprehensive testing approach:

- **Unit Tests**: Component and utility function testing
- **Integration Tests**: Feature and workflow testing
- **E2E Tests**: End-to-end user journey testing
- **Visual Tests**: Component visual regression testing
- **Accessibility Tests**: A11y compliance testing

## 🛠️ Testing Tools

### Core Testing Stack

- **Vitest**: Fast unit test runner
- **React Testing Library**: Component testing utilities
- **Playwright**: End-to-end testing framework
- **MSW**: API mocking
- **vitest-axe**: Accessibility testing
- **Storybook**: Visual testing and documentation

### Configuration Files

- `vitest.config.ts`: Main Vitest configuration
- `vitest.unit.config.ts`: Unit test specific configuration
- `playwright.config.ts`: Playwright E2E configuration
- `vitest.shims.d.ts`: Type definitions for testing

## 🔧 Unit Testing

### Setup

Unit tests use Vitest with React Testing Library:

```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { Button } from './Button';
```

### Component Testing

```typescript
describe('Button Component', () => {
  it('renders with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button')).toHaveTextContent('Click me');
  });

  it('calls onClick when clicked', () => {
    const handleClick = vi.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('is disabled when disabled prop is true', () => {
    render(<Button disabled>Click me</Button>);
    expect(screen.getByRole('button')).toBeDisabled();
  });
});
```

### Testing Hooks

```typescript
import { renderHook, act } from '@testing-library/react';
import { useCounter } from './useCounter';

describe('useCounter Hook', () => {
  it('initializes with default value', () => {
    const { result } = renderHook(() => useCounter());
    expect(result.current.count).toBe(0);
  });

  it('increments count', () => {
    const { result } = renderHook(() => useCounter());
    
    act(() => {
      result.current.increment();
    });
    
    expect(result.current.count).toBe(1);
  });
});
```

### Mocking

#### API Calls with MSW

```typescript
import { rest } from 'msw';
import { setupServer } from 'msw/node';

const server = setupServer(
  rest.get('/api/users', (req, res, ctx) => {
    return res(ctx.json([{ id: 1, name: 'John Doe' }]));
  })
);

beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());
```

#### Module Mocking

```typescript
import { vi } from 'vitest';

// Mock external module
vi.mock('@/services/api', () => ({
  fetchUsers: vi.fn(() => Promise.resolve([])),
}));

// Mock React Router
vi.mock('react-router-dom', () => ({
  useNavigate: () => vi.fn(),
  useLocation: () => ({ pathname: '/' }),
}));
```

### Running Unit Tests

```bash
# Run tests in watch mode
npm run test

# Run tests once
npm run test:run

# Run with coverage
npm run test:coverage

# Run tests with UI
npm run test:ui
```

## 🎭 E2E Testing

### Playwright Setup

E2E tests use Playwright for cross-browser testing:

```typescript
import { test, expect } from '@playwright/test';

test.describe('Login Flow', () => {
  test('should login successfully', async ({ page }) => {
    await page.goto('/login');
    
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'password123');
    await page.click('[data-testid="login-button"]');
    
    await expect(page).toHaveURL('/dashboard');
    await expect(page.locator('[data-testid="welcome-message"]')).toBeVisible();
  });
});
```

### Page Object Model

```typescript
export class LoginPage {
  constructor(private page: Page) {}

  async goto() {
    await this.page.goto('/login');
  }

  async login(email: string, password: string) {
    await this.page.fill('[data-testid="email-input"]', email);
    await this.page.fill('[data-testid="password-input"]', password);
    await this.page.click('[data-testid="login-button"]');
  }

  async expectLoginError() {
    await expect(this.page.locator('[data-testid="error-message"]')).toBeVisible();
  }
}
```

### Running E2E Tests

```bash
# Run E2E tests
npm run test:e2e

# Run with UI mode
npm run test:e2e:ui

# Run in headed mode
npm run test:e2e:headed
```

## 🎨 Visual Testing

### Storybook Integration

Visual tests are integrated with Storybook:

```typescript
// Button.stories.tsx
import type { Meta, StoryObj } from '@storybook/react';
import { Button } from './Button';

const meta: Meta<typeof Button> = {
  title: 'Components/Button',
  component: Button,
  parameters: {
    layout: 'centered',
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  args: {
    variant: 'primary',
    children: 'Button',
  },
};

export const Secondary: Story = {
  args: {
    variant: 'secondary',
    children: 'Button',
  },
};
```

### Visual Regression Testing

```bash
# Build Storybook
npm run build-storybook

# Run visual tests (if configured)
npm run test:visual
```

## ♿ Accessibility Testing

### vitest-axe Integration

```typescript
import { render } from '@testing-library/react';
import { axe, toHaveNoViolations } from 'vitest-axe';
import { Button } from './Button';

expect.extend(toHaveNoViolations);

describe('Button Accessibility', () => {
  it('should not have accessibility violations', async () => {
    const { container } = render(<Button>Click me</Button>);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});
```

### Manual Accessibility Testing

1. **Keyboard Navigation**: Test with Tab, Enter, Space, Arrow keys
2. **Screen Reader**: Test with NVDA, JAWS, or VoiceOver
3. **Color Contrast**: Verify contrast ratios meet WCAG guidelines
4. **Focus Management**: Ensure proper focus indicators

## 📊 Test Coverage

### Coverage Configuration

```typescript
// vitest.config.ts
export default defineConfig({
  test: {
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/stories/**',
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80,
        },
      },
    },
  },
});
```

### Coverage Reports

```bash
# Generate coverage report
npm run test:coverage

# View HTML coverage report
open coverage/index.html
```

## 🚀 Best Practices

### Test Organization

1. **Describe Blocks**: Group related tests
2. **Clear Test Names**: Describe what is being tested
3. **Arrange-Act-Assert**: Structure tests clearly
4. **Single Assertion**: One assertion per test when possible

### Test Data

1. **Test Factories**: Create reusable test data generators
2. **Fixtures**: Use consistent test data
3. **Mocking**: Mock external dependencies
4. **Cleanup**: Clean up after tests

### Performance

1. **Parallel Execution**: Run tests in parallel
2. **Test Isolation**: Ensure tests don't depend on each other
3. **Selective Testing**: Run only relevant tests during development
4. **Fast Feedback**: Optimize for quick test execution

### Debugging

1. **Debug Mode**: Use `--inspect` flag for debugging
2. **Console Logs**: Add temporary logs for debugging
3. **Test Isolation**: Run single tests to isolate issues
4. **Browser DevTools**: Use Playwright's debug mode

## 📝 Testing Checklist

### Component Tests

- [ ] Renders correctly with default props
- [ ] Handles all prop variations
- [ ] Responds to user interactions
- [ ] Shows correct states (loading, error, success)
- [ ] Passes accessibility tests
- [ ] Handles edge cases gracefully

### Integration Tests

- [ ] Components work together correctly
- [ ] Data flows properly between components
- [ ] API integrations work as expected
- [ ] Error handling works across components

### E2E Tests

- [ ] Critical user journeys work end-to-end
- [ ] Authentication flows work correctly
- [ ] Forms submit and validate properly
- [ ] Navigation works across the application

## 🔍 Troubleshooting

### Common Issues

1. **Async Testing**: Use `waitFor` for async operations
2. **Timer Mocking**: Mock timers for time-dependent tests
3. **DOM Cleanup**: Ensure proper cleanup between tests
4. **Mock Persistence**: Reset mocks between tests

### Debugging Tips

1. **Screen Debug**: Use `screen.debug()` to see rendered DOM
2. **Query Debugging**: Use `screen.logTestingPlaygroundURL()`
3. **Playwright Trace**: Enable trace for E2E debugging
4. **Test Isolation**: Run tests individually to isolate issues
